- https://www.moneycontrol.com/markets/sector-analysis/#google_vignette
https://www.moneycontrol.com/markets/stock-ideas/#goog_rewarded




in /api/investments/stats get all investment from schema that has purpose OWNED and type STOCK get all invesement doc that mathced this condition
then go through each stock and get the today stock price using existing strategy and caluculate the profit loss by buyed price - today price

FInally i need data in this format
{
   "totalInvestment": 0,
    "totalCurrentValue": 0,
    "totalReturn": 0,
    "returnPercentage": 0,
    "investmentsByType": [],
    "recentInvestments": [],
    "performanceData": [],
}

Need to redesign the dashboard page



- Need to refactored the code base 
- redisgn the dashboard page
- AI integration for stock analysis