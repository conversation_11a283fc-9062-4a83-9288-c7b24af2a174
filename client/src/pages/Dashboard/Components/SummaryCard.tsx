"use client";

import { useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";
import { Expense, EXPENSE_TYPE } from "@/services/expense.service";
import { formatCurrency } from "@/lib/utils";

interface TransactionSummaryCardsProps {
  transactions: Expense[];
  isLoading: boolean;
}

interface SummaryCard {
  name: string;
  total: number;
  type: string;
  color: string;
  icon: string;
}

// Helper function to get category icons
const getCategoryIcon = (category: string): string => {
  const categoryIcons: Record<string, string> = {
    Income: "💰",
    Housing: "🏠",
    Food: "🍔",
    Transportation: "🚗",
    Entertainment: "🎬",
    Utilities: "💡",
    Debt: "💳",
    Investment: "📈",
    Tax: "📝",
    Other: "📦",
    medical: "🏥",
    bills: "📄",
    entertainment: "🎬",
  };
  return categoryIcons[category] || "📦";
};

export function TransactionSummaryCards({
  transactions,
  isLoading,
}: TransactionSummaryCardsProps) {
  const summaryCards = useMemo(() => {
    const cards: SummaryCard[] = [];

    // Group debt transactions by name
    const debtByName = new Map<string, { total: number; type: EXPENSE_TYPE }>();

    // Group other transactions by category
    const otherByCategory = new Map<
      string,
      { total: number; types: Set<EXPENSE_TYPE> }
    >();

    transactions.forEach((transaction) => {
      if (
        transaction.type === EXPENSE_TYPE.DEBT_BOUGHT ||
        transaction.type === EXPENSE_TYPE.DEBT_GIVEN
      ) {
        // Group debt transactions by name
        const name = transaction.name || "Unnamed";
        const existing = debtByName.get(name);
        if (existing) {
          existing.total += transaction.amount;
        } else {
          debtByName.set(name, {
            total: transaction.amount,
            type: transaction.type,
          });
        }
      } else {
        // Group other transactions by category
        const category = transaction.category;
        const existing = otherByCategory.get(category);
        if (existing) {
          existing.total += transaction.amount;
          existing.types.add(transaction.type);
        } else {
          otherByCategory.set(category, {
            total: transaction.amount,
            types: new Set([transaction.type]),
          });
        }
      }
    });

    // Convert debt groups to cards
    debtByName.forEach((data, name) => {
      const isDebtBought = data.type === EXPENSE_TYPE.DEBT_BOUGHT;
      cards.push({
        name,
        total: data.total,
        type: isDebtBought ? "debt_bought" : "debt_given",
        color: isDebtBought ? "text-red-600" : "text-orange-600",
        icon: isDebtBought ? "💳" : "🤝",
      });
    });

    // Convert category groups to cards
    otherByCategory.forEach((data, category) => {
      const hasIncome = data.types.has(EXPENSE_TYPE.INCOME);
      const hasExpense = data.types.has(EXPENSE_TYPE.EXPENSE);

      let color = "text-gray-600";
      let icon = "📦";

      if (hasIncome && !hasExpense) {
        color = "text-green-600";
        icon = "💰";
      } else if (hasExpense && !hasIncome) {
        color = "text-red-600";
        icon = getCategoryIcon(category);
      } else if (hasIncome && hasExpense) {
        color = "text-blue-600";
        icon = getCategoryIcon(category);
      }

      cards.push({
        name: category,
        total: data.total,
        type: "category",
        color,
        icon,
      });
    });

    // Sort cards by absolute total (highest first)
    return cards.sort((a, b) => Math.abs(b.total) - Math.abs(a.total));
  }, [transactions]);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="p-4">
            <CardContent className="p-0">
              <Skeleton className="h-4 w-20 mb-2" />
              <Skeleton className="h-6 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (summaryCards.length === 0) {
    return null;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {summaryCards.slice(0, 8).map((card, index) => (
        <motion.div
          key={`${card.name}-${card.type}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{card.icon}</span>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground truncate max-w-[120px]">
                      {card.name}
                    </p>
                    <p className={`text-lg font-semibold ${card.color}`}>
                      {formatCurrency(Math.abs(card.total))}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}
