import { Button } from "@/components/ui/button";
import { StockDataType } from "@/services/investment.service";
import { StocksChart } from "@/shared/components/charts/StockChart";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@radix-ui/react-tabs";
import { Loader2 } from "lucide-react";

export function MarketData({
  isLoadingStocks,
  commoditiesData,
  stocksData,
  ownedStocksData,
  monitoredStocksData,
  mutualFundsData,
  stocksFilter,
  setStocksFilter,
}: {
  isLoadingStocks: boolean;
  commoditiesData: StockDataType[];
  stocksData: StockDataType[];
  ownedStocksData: StockDataType[];
  monitoredStocksData: StockDataType[];
  mutualFundsData: StockDataType[];
  stocksFilter: "all" | "owned" | "monitored";
  setStocksFilter: React.Dispatch<
    React.SetStateAction<"all" | "owned" | "monitored">
  >;
}) {
  return (
    <>
      <div className="h-full">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">Market Data</h2>
          <p className="text-sm text-muted-foreground">
            Track the performance of your investments in the market
          </p>
        </div>

        {isLoadingStocks ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : !isLoadingStocks ? (
          <Tabs defaultValue="commodities" className="mt-6">
            <TabsList className="mb-4">
              <TabsTrigger value="commodities">
                Gold/Silver/Currency
              </TabsTrigger>
              <TabsTrigger value="stocks">Stocks</TabsTrigger>
              <TabsTrigger value="mutual-funds">Mutual Funds</TabsTrigger>
            </TabsList>

            <TabsContent value="commodities" className="mt-0">
              {commoditiesData.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                  {commoditiesData.map((item) => (
                    <StocksChart folio={[item]} key={item.name} />
                  ))}
                </div>
              ) : (
                <div className="flex justify-center items-center h-40 text-muted-foreground">
                  No commodities data available
                </div>
              )}
            </TabsContent>

            <TabsContent value="stocks" className="mt-0">
              <div className="mb-4 flex items-center gap-2">
                <span className="text-sm font-medium">Filter:</span>
                <div className="flex gap-2">
                  <Button
                    variant={stocksFilter === "all" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStocksFilter("all")}
                  >
                    All
                  </Button>
                  <Button
                    variant={stocksFilter === "owned" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStocksFilter("owned")}
                  >
                    Owned
                  </Button>
                  <Button
                    variant={
                      stocksFilter === "monitored" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => setStocksFilter("monitored")}
                  >
                    Monitoring
                  </Button>
                </div>
              </div>

              {stocksData.length > 0 ? (
                <>
                  {stocksFilter === "all" && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                      {stocksData.map((item) => (
                        <StocksChart folio={[item]} key={item.name} />
                      ))}
                    </div>
                  )}

                  {stocksFilter === "owned" && (
                    <>
                      {ownedStocksData.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                          {ownedStocksData.map((item) => (
                            <StocksChart folio={[item]} key={item.name} />
                          ))}
                        </div>
                      ) : (
                        <div className="flex justify-center items-center h-40 text-muted-foreground">
                          No owned stocks data available
                        </div>
                      )}
                    </>
                  )}

                  {stocksFilter === "monitored" && (
                    <>
                      {monitoredStocksData.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                          {monitoredStocksData.map((item) => (
                            <StocksChart folio={[item]} key={item.name} />
                          ))}
                        </div>
                      ) : (
                        <div className="flex justify-center items-center h-40 text-muted-foreground">
                          No monitored stocks data available
                        </div>
                      )}
                    </>
                  )}
                </>
              ) : (
                <div className="flex justify-center items-center h-40 text-muted-foreground">
                  No stocks data available
                </div>
              )}
            </TabsContent>

            <TabsContent value="mutual-funds" className="mt-0">
              {mutualFundsData.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                  {mutualFundsData.map((item) => (
                    <StocksChart folio={[item]} key={item.name} />
                  ))}
                </div>
              ) : (
                <div className="flex justify-center items-center h-40 text-muted-foreground">
                  No mutual funds data available
                </div>
              )}
            </TabsContent>
          </Tabs>
        ) : (
          <div className="flex justify-center items-center h-64 text-muted-foreground">
            No market data available
          </div>
        )}
      </div>
    </>
  );
}
