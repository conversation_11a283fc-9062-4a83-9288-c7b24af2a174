import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { getExpenseStats, ExpenseStats } from "@/services/expense.service";

interface ExpenseContextType {
  expenseStats: ExpenseStats | null;
  loading: boolean;
  error: boolean;
  refreshData: () => void;
  refreshTrigger: number;
  dateRange: {
    from?: Date;
    to?: Date;
  };
  setDateRange: (dateRange: { from?: Date; to?: Date }) => void;
  ignoreDate: boolean;
  setIgnoreDate: (ignore: boolean) => void;
}

const ExpenseContext = createContext<ExpenseContextType | undefined>(undefined);

export function ExpenseProvider({ children }: { children: ReactNode }) {
  const [expenseStats, setExpenseStats] = useState<ExpenseStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [ignoreDate, setIgnoreDate] = useState(false);

  const today = new Date();
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({
    from: firstDayOfMonth,
    to: lastDayOfMonth,
  });

  const refreshData = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  useEffect(() => {
    const fetchExpenseStats = async () => {
      try {
        setLoading(true);
        const stats = await getExpenseStats(dateRange, ignoreDate);
        setExpenseStats(stats);
        setError(false);
      } catch (err) {
        console.error("Failed to fetch expense statistics:", err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    fetchExpenseStats();
  }, [refreshTrigger, dateRange, ignoreDate]);

  return (
    <ExpenseContext.Provider
      value={{
        expenseStats,
        loading,
        error,
        refreshData,
        refreshTrigger,
        dateRange,
        setDateRange,
        ignoreDate,
        setIgnoreDate,
      }}
    >
      {children}
    </ExpenseContext.Provider>
  );
}

export function useExpense() {
  const context = useContext(ExpenseContext);
  if (context === undefined) {
    throw new Error("useExpense must be used within an ExpenseProvider");
  }
  return context;
}
