/* eslint-disable @typescript-eslint/no-explicit-any */

// Base color system
export const COLORS = {
  // Primary colors
  primary: "hsl(var(--primary))",
  primaryForeground: "hsl(var(--primary-foreground))",

  // Secondary colors
  secondary: "hsl(var(--secondary))",
  secondaryForeground: "hsl(var(--secondary-foreground))",

  // Semantic colors
  success: "hsl(142.1, 76.2%, 36.3%)",
  successLight: "hsl(142.1, 76.2%, 46.3%)",
  successDark: "hsl(142.1, 76.2%, 26.3%)",

  danger: "hsl(346.8, 77.2%, 49.8%)",
  dangerLight: "hsl(346.8, 77.2%, 59.8%)",
  dangerDark: "hsl(346.8, 77.2%, 39.8%)",

  warning: "hsl(47.9, 95.8%, 53.1%)",
  warningLight: "hsl(47.9, 95.8%, 63.1%)",
  warningDark: "hsl(47.9, 95.8%, 43.1%)",

  info: "hsl(221.2, 83.2%, 53.3%)",
  infoLight: "hsl(221.2, 83.2%, 63.3%)",
  infoDark: "hsl(221.2, 83.2%, 43.3%)",

  // Neutral colors
  muted: "hsl(var(--muted))",
  mutedForeground: "hsl(var(--muted-foreground))",
  background: "hsl(var(--background))",
  foreground: "hsl(var(--foreground))",
  border: "hsl(var(--border))",

  // Chart specific colors
  chart1: "#0088FE",
  chart2: "#00C49F",
  chart3: "#FFBB28",
  chart4: "#FF8042",
  chart5: "#8884D8",
  chart6: "#82ca9d",
  chart7: "#ffc658",
  chart8: "#ff7300",
} as const;

// Color palettes for different use cases
export const COLOR_PALETTES = {
  // Default palette for general charts
  default: [
    COLORS.chart1,
    COLORS.chart2,
    COLORS.chart3,
    COLORS.chart4,
    COLORS.chart5,
    COLORS.chart6,
    COLORS.chart7,
    COLORS.chart8,
  ],

  // Financial data palette
  financial: [
    COLORS.success,
    COLORS.danger,
    COLORS.warning,
    COLORS.info,
    COLORS.primary,
    COLORS.secondary,
  ],

  // Investment specific palette
  investment: [
    "#0088FE", // Blue
    "#00C49F", // Teal
    "#FFBB28", // Yellow
    "#FF8042", // Orange
    "#8884D8", // Purple
    "#82ca9d", // Light green
    "#ffc658", // Light yellow
    "#ff7300", // Dark orange
  ],

  // Expense categories palette
  expense: [
    COLORS.danger, // Food & Dining
    COLORS.warning, // Transportation
    COLORS.info, // Shopping
    COLORS.success, // Entertainment
    COLORS.chart5, // Bills & Utilities
    COLORS.chart6, // Healthcare
    COLORS.chart7, // Education
    COLORS.chart8, // Travel
  ],

  // Status palette
  status: [
    COLORS.success, // Active/Positive
    COLORS.warning, // Pending/Neutral
    COLORS.danger, // Inactive/Negative
    COLORS.info, // Info/Processing
    COLORS.muted, // Disabled/Unknown
  ],

  // Gradient palettes
  gradients: {
    success: [COLORS.successLight, COLORS.success, COLORS.successDark],
    danger: [COLORS.dangerLight, COLORS.danger, COLORS.dangerDark],
    warning: [COLORS.warningLight, COLORS.warning, COLORS.warningDark],
    info: [COLORS.infoLight, COLORS.info, COLORS.infoDark],
  },
} as const;

// Specific color mappings for business logic
export const EXPENSE_TYPE_COLORS = {
  INCOME: COLORS.success,
  EXPENSE: COLORS.danger,
  DEBT_BOUGHT: COLORS.warning,
  DEBT_GIVEN: COLORS.info,
} as const;

export const INVESTMENT_TYPE_COLORS = {
  STOCKS: COLORS.chart1,
  BONDS: COLORS.chart2,
  MUTUAL_FUNDS: COLORS.chart3,
  ETF: COLORS.chart4,
  CRYPTO: COLORS.chart5,
  REAL_ESTATE: COLORS.chart6,
  COMMODITIES: COLORS.chart7,
  OTHER: COLORS.chart8,
} as const;

export const PRIORITY_COLORS = {
  HIGH: COLORS.danger,
  MEDIUM: COLORS.warning,
  LOW: COLORS.success,
  NONE: COLORS.muted,
} as const;

// Utility functions for color manipulation
export const getColorWithOpacity = (color: string, opacity: number): string => {
  // Convert HSL to HSLA
  if (color.startsWith("hsl(")) {
    return color.replace("hsl(", "hsla(").replace(")", `, ${opacity})`);
  }

  // Convert hex to rgba
  if (color.startsWith("#")) {
    const hex = color.replace("#", "");
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  return color;
};

export const getContrastColor = (backgroundColor: any): string => {
  // Simple contrast logic - in a real app, you might want more sophisticated color contrast calculation
  const darkColors = [
    COLORS.danger,
    COLORS.success,
    COLORS.info,
    COLORS.chart5,
  ];

  if (darkColors.includes(backgroundColor)) {
    return "#ffffff";
  }

  return "#000000";
};

// Color theme configurations
export const CHART_THEMES = {
  light: {
    background: "#ffffff",
    text: "#374151",
    grid: "#f3f4f6",
    axis: "#9ca3af",
  },
  dark: {
    background: "#1f2937",
    text: "#f9fafb",
    grid: "#374151",
    axis: "#6b7280",
  },
} as const;

// Export commonly used color combinations
export const COMMON_COMBINATIONS = {
  incomeExpense: [COLORS.success, COLORS.danger],
  positiveNegative: [COLORS.success, COLORS.danger],
  primarySecondary: [COLORS.primary, COLORS.secondary],
  warningDanger: [COLORS.warning, COLORS.danger],
} as const;
