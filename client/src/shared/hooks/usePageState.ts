import { useState, useCallback } from "react";

// Generic page state interface
export interface PageState<T = unknown> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// Generic page actions
export interface PageActions<T = unknown> {
  setData: (data: T) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  refresh: () => Promise<void>;
  reset: () => void;
}

// Hook for managing common page state
export function usePageState<T = unknown>(
  initialData: T | null = null,
  fetchFunction?: () => Promise<T>
): [PageState<T>, PageActions<T>] {
  const [state, setState] = useState<PageState<T>>({
    data: initialData,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const setData = useCallback((data: T) => {
    setState((prev) => ({
      ...prev,
      data,
      error: null,
      lastUpdated: new Date(),
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState((prev) => ({
      ...prev,
      loading,
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState((prev) => ({
      ...prev,
      error,
      loading: false,
    }));
  }, []);

  const refresh = useCallback(async () => {
    if (!fetchFunction) return;

    setLoading(true);
    setError(null);

    try {
      const data = await fetchFunction();
      setData(data);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An error occurred";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [fetchFunction, setData, setError, setLoading]);

  const reset = useCallback(() => {
    setState({
      data: initialData,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, [initialData]);

  const actions: PageActions<T> = {
    setData,
    setLoading,
    setError,
    refresh,
    reset,
  };

  return [state, actions];
}

// Hook for managing list state with pagination
export interface ListState<T = unknown> {
  items: T[];
  loading: boolean;
  error: string | null;
  page: number;
  pageSize: number;
  total: number;
  hasMore: boolean;
}

export interface ListActions<T = unknown> {
  setItems: (items: T[]) => void;
  addItems: (items: T[]) => void;
  updateItem: (id: string | number, updates: Partial<T>) => void;
  removeItem: (id: string | number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  nextPage: () => void;
  prevPage: () => void;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  reset: () => void;
}

export function useListState<T extends { id: string | number }>(
  initialPageSize: number = 10
): [ListState<T>, ListActions<T>] {
  const [state, setState] = useState<ListState<T>>({
    items: [],
    loading: false,
    error: null,
    page: 1,
    pageSize: initialPageSize,
    total: 0,
    hasMore: false,
  });

  const setItems = useCallback((items: T[]) => {
    setState((prev) => ({
      ...prev,
      items,
      error: null,
    }));
  }, []);

  const addItems = useCallback((newItems: T[]) => {
    setState((prev) => ({
      ...prev,
      items: [...prev.items, ...newItems],
      error: null,
    }));
  }, []);

  const updateItem = useCallback((id: string | number, updates: Partial<T>) => {
    setState((prev) => ({
      ...prev,
      items: prev.items.map((item) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    }));
  }, []);

  const removeItem = useCallback((id: string | number) => {
    setState((prev) => ({
      ...prev,
      items: prev.items.filter((item) => item.id !== id),
      total: Math.max(0, prev.total - 1),
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState((prev) => ({
      ...prev,
      loading,
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState((prev) => ({
      ...prev,
      error,
      loading: false,
    }));
  }, []);

  const nextPage = useCallback(() => {
    setState((prev) => ({
      ...prev,
      page: prev.hasMore ? prev.page + 1 : prev.page,
    }));
  }, []);

  const prevPage = useCallback(() => {
    setState((prev) => ({
      ...prev,
      page: Math.max(1, prev.page - 1),
    }));
  }, []);

  const setPage = useCallback((page: number) => {
    setState((prev) => ({
      ...prev,
      page: Math.max(1, page),
    }));
  }, []);

  const setPageSize = useCallback((pageSize: number) => {
    setState((prev) => ({
      ...prev,
      pageSize: Math.max(1, pageSize),
      page: 1, // Reset to first page when changing page size
    }));
  }, []);

  const reset = useCallback(() => {
    setState({
      items: [],
      loading: false,
      error: null,
      page: 1,
      pageSize: initialPageSize,
      total: 0,
      hasMore: false,
    });
  }, [initialPageSize]);

  const actions: ListActions<T> = {
    setItems,
    addItems,
    updateItem,
    removeItem,
    setLoading,
    setError,
    nextPage,
    prevPage,
    setPage,
    setPageSize,
    reset,
  };

  return [state, actions];
}

// Hook for managing form state
export interface FormState<T = Record<string, unknown>> {
  values: T;
  errors: Record<keyof T, string>;
  touched: Record<keyof T, boolean>;
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
}

export interface FormActions<T = Record<string, unknown>> {
  setValue: (field: keyof T, value: unknown) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: keyof T, error: string) => void;
  setErrors: (errors: Partial<Record<keyof T, string>>) => void;
  setTouched: (field: keyof T, touched: boolean) => void;
  setSubmitting: (submitting: boolean) => void;
  reset: () => void;
  validate: () => boolean;
}

export function useFormState<T extends Record<string, unknown>>(
  initialValues: T,
  validationRules?: Record<keyof T, (value: unknown) => string | null>
): [FormState<T>, FormActions<T>] {
  const [state, setState] = useState<FormState<T>>({
    values: initialValues,
    errors: {} as Record<keyof T, string>,
    touched: {} as Record<keyof T, boolean>,
    isValid: true,
    isDirty: false,
    isSubmitting: false,
  });

  const setValue = useCallback(
    (field: keyof T, value: unknown) => {
      setState((prev) => {
        const newValues = { ...prev.values, [field]: value };
        const isDirty =
          JSON.stringify(newValues) !== JSON.stringify(initialValues);

        // Validate field if rules exist
        let newErrors = { ...prev.errors };
        if (validationRules && validationRules[field]) {
          const error = validationRules[field](value);
          if (error) {
            newErrors[field] = error;
          } else {
            delete newErrors[field];
          }
        }

        const isValid = Object.keys(newErrors).length === 0;

        return {
          ...prev,
          values: newValues,
          errors: newErrors,
          touched: { ...prev.touched, [field]: true },
          isValid,
          isDirty,
        };
      });
    },
    [initialValues, validationRules]
  );

  const setValues = useCallback((values: Partial<T>) => {
    setState((prev) => ({
      ...prev,
      values: { ...prev.values, ...values },
      isDirty: true,
    }));
  }, []);

  const setError = useCallback((field: keyof T, error: string) => {
    setState((prev) => ({
      ...prev,
      errors: { ...prev.errors, [field]: error },
      isValid: false,
    }));
  }, []);

  const setErrors = useCallback((errors: Partial<Record<keyof T, string>>) => {
    setState((prev) => ({
      ...prev,
      errors: { ...prev.errors, ...errors },
      isValid: Object.keys(errors).length === 0,
    }));
  }, []);

  const setTouched = useCallback((field: keyof T, touched: boolean) => {
    setState((prev) => ({
      ...prev,
      touched: { ...prev.touched, [field]: touched },
    }));
  }, []);

  const setSubmitting = useCallback((submitting: boolean) => {
    setState((prev) => ({
      ...prev,
      isSubmitting: submitting,
    }));
  }, []);

  const reset = useCallback(() => {
    setState({
      values: initialValues,
      errors: {} as Record<keyof T, string>,
      touched: {} as Record<keyof T, boolean>,
      isValid: true,
      isDirty: false,
      isSubmitting: false,
    });
  }, [initialValues]);

  const validate = useCallback(() => {
    if (!validationRules) return true;

    const newErrors: Record<keyof T, string> = {} as Record<keyof T, string>;

    Object.keys(validationRules).forEach((field) => {
      const error = validationRules[field as keyof T](
        state.values[field as keyof T]
      );
      if (error) {
        newErrors[field as keyof T] = error;
      }
    });

    setState((prev) => ({
      ...prev,
      errors: newErrors,
      isValid: Object.keys(newErrors).length === 0,
    }));

    return Object.keys(newErrors).length === 0;
  }, [validationRules, state.values]);

  const actions: FormActions<T> = {
    setValue,
    setValues,
    setError,
    setErrors,
    setTouched,
    setSubmitting,
    reset,
    validate,
  };

  return [state, actions];
}
