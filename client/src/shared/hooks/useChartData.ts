import { useMemo } from "react";
import {
  transformMonthlyData,
  transformCategoryData,
  transformPerformanceData,
  validateChartData,
  validatePieData,
  validateLineData,
  validateBarData,
  MonthlyDataItem,
  PerformanceDataItem,
  ChartDataItem,
  PieDataItem,
} from "../utils/chartHelpers";
import { COLOR_PALETTES } from "../constants/colors";

// Hook for processing monthly chart data
export function useMonthlyChartData(rawData: MonthlyDataItem[]) {
  return useMemo(() => {
    if (!validateChartData(rawData)) {
      return {
        data: [],
        isValid: false,
        isEmpty: true,
      };
    }

    const transformedData = transformMonthlyData(rawData);

    return {
      data: transformedData,
      isValid: validateBarData(transformedData),
      isEmpty: transformedData.length === 0,
    };
  }, [rawData]);
}

// Hook for processing category pie chart data
export function useCategoryChartData(
  rawData: Record<string, number> | undefined
) {
  return useMemo(() => {
    if (!rawData || Object.keys(rawData).length === 0) {
      return {
        data: [],
        isValid: false,
        isEmpty: true,
        colors: [],
      };
    }

    const transformedData = transformCategoryData(rawData);
    const colors = COLOR_PALETTES.default.slice(0, transformedData.length);

    return {
      data: transformedData,
      isValid: validatePieData(transformedData),
      isEmpty: transformedData.length === 0,
      colors,
    };
  }, [rawData]);
}

// Hook for processing performance line chart data
export function usePerformanceChartData(rawData: PerformanceDataItem[]) {
  return useMemo(() => {
    if (!validateChartData(rawData)) {
      return {
        data: [],
        isValid: false,
        isEmpty: true,
      };
    }

    const transformedData = transformPerformanceData(rawData);

    return {
      data: transformedData,
      isValid: validateLineData(transformedData),
      isEmpty: transformedData.length === 0,
    };
  }, [rawData]);
}

// Hook for processing investment type distribution data
export function useInvestmentTypeData(rawData: PieDataItem[]) {
  return useMemo(() => {
    if (!validateChartData(rawData)) {
      return {
        data: [],
        isValid: false,
        isEmpty: true,
        colors: [],
      };
    }

    const colors = COLOR_PALETTES.investment.slice(0, rawData.length);

    return {
      data: rawData,
      isValid: validatePieData(rawData),
      isEmpty: rawData.length === 0,
      colors,
    };
  }, [rawData]);
}

// Hook for processing expense data with financial colors
export function useExpenseChartData(
  rawData: ChartDataItem[],
  chartType: "bar" | "pie" | "line" = "bar"
) {
  return useMemo(() => {
    if (!validateChartData(rawData)) {
      return {
        data: [],
        isValid: false,
        isEmpty: true,
        colors: [],
        bars: [],
        lines: [],
      };
    }

    const colors = COLOR_PALETTES.financial.slice(0, rawData.length);

    // Create bar configurations for financial data
    const bars = [
      {
        dataKey: "income",
        fill: colors[0] || "#00C49F",
        name: "Income",
      },
      {
        dataKey: "expenses",
        fill: colors[1] || "#FF8042",
        name: "Expenses",
      },
    ];

    // Create line configurations for financial data
    const lines = [
      {
        dataKey: "income",
        stroke: colors[0] || "#00C49F",
        name: "Income",
      },
      {
        dataKey: "expenses",
        stroke: colors[1] || "#FF8042",
        name: "Expenses",
      },
    ];

    let isValid = false;
    switch (chartType) {
      case "bar":
        isValid = validateBarData(rawData);
        break;
      case "pie":
        isValid = validatePieData(rawData);
        break;
      case "line":
        isValid = validateLineData(rawData);
        break;
    }

    return {
      data: rawData,
      isValid,
      isEmpty: rawData.length === 0,
      colors,
      bars,
      lines,
    };
  }, [rawData, chartType]);
}

// Hook for processing table data with sorting and filtering
export function useTableData<T extends Record<string, unknown>>(
  rawData: T[],
  sortConfig?: { key: keyof T; direction: "asc" | "desc" },
  filterConfig?: { key: keyof T; value: unknown }
) {
  return useMemo(() => {
    if (!Array.isArray(rawData)) {
      return {
        data: [],
        isEmpty: true,
        total: 0,
      };
    }

    let processedData = [...rawData];

    // Apply filtering
    if (filterConfig) {
      processedData = processedData.filter((item) => {
        const itemValue = item[filterConfig.key];
        if (typeof filterConfig.value === "string") {
          return itemValue
            ?.toString()
            .toLowerCase()
            .includes(filterConfig.value.toLowerCase());
        }
        return itemValue === filterConfig.value;
      });
    }

    // Apply sorting
    if (sortConfig) {
      processedData.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (aValue < bValue) {
          return sortConfig.direction === "asc" ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === "asc" ? 1 : -1;
        }
        return 0;
      });
    }

    return {
      data: processedData,
      isEmpty: processedData.length === 0,
      total: processedData.length,
    };
  }, [rawData, sortConfig, filterConfig]);
}

// Hook for processing stats card data
export function useStatsData(rawStats: Record<string, number> | undefined) {
  return useMemo(() => {
    if (!rawStats) {
      return {
        cards: [],
        isEmpty: true,
        total: 0,
      };
    }

    const cards = Object.entries(rawStats).map(([key, value], index) => ({
      title: key
        .replace(/([A-Z])/g, " $1")
        .replace(/^./, (str) => str.toUpperCase()),
      value,
      key,
      colorIndex: index % COLOR_PALETTES.default.length,
    }));

    return {
      cards,
      isEmpty: cards.length === 0,
      total: cards.length,
    };
  }, [rawStats]);
}

// Time series data interface
interface TimeSeriesDataItem {
  date: string;
  [key: string]: unknown;
}

// Hook for processing time series data
export function useTimeSeriesData(
  rawData: TimeSeriesDataItem[],
  dateRange?: { from: Date; to: Date }
) {
  return useMemo(() => {
    if (!validateChartData(rawData)) {
      return {
        data: [],
        isValid: false,
        isEmpty: true,
        dateRange: null,
      };
    }

    let processedData = [...rawData];

    // Apply date filtering if provided
    if (dateRange) {
      processedData = processedData.filter((item) => {
        const itemDate = new Date(item.date);
        return itemDate >= dateRange.from && itemDate <= dateRange.to;
      });
    }

    // Sort by date
    processedData.sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Transform dates for display
    const transformedData = processedData.map((item) => ({
      ...item,
      name: new Date(item.date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
    }));

    return {
      data: transformedData,
      isValid: validateLineData(transformedData),
      isEmpty: transformedData.length === 0,
      dateRange:
        processedData.length > 0
          ? {
              from: new Date(processedData[0].date),
              to: new Date(processedData[processedData.length - 1].date),
            }
          : null,
    };
  }, [rawData, dateRange]);
}

// Aggregated data interface
interface AggregatedDataItem {
  date: string;
  amount: number;
  [key: string]: unknown;
}

// Hook for aggregating data by period
export function useAggregatedData(
  rawData: AggregatedDataItem[],
  period: "day" | "week" | "month" | "year" = "month"
) {
  return useMemo(() => {
    if (!validateChartData(rawData)) {
      return {
        data: [],
        isEmpty: true,
        total: 0,
      };
    }

    interface AggregatedResult {
      period: string;
      amount: number;
      count: number;
      items: AggregatedDataItem[];
    }

    const aggregated = rawData.reduce((acc, item) => {
      const date = new Date(item.date);
      let key: string;

      switch (period) {
        case "day":
          key = date.toISOString().split("T")[0];
          break;
        case "week":
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().split("T")[0];
          break;
        case "month":
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
            2,
            "0"
          )}`;
          break;
        case "year":
          key = String(date.getFullYear());
          break;
        default:
          key = date.toISOString().split("T")[0];
      }

      if (!acc[key]) {
        acc[key] = {
          period: key,
          amount: 0,
          count: 0,
          items: [],
        };
      }

      acc[key].amount += item.amount;
      acc[key].count += 1;
      acc[key].items.push(item);

      return acc;
    }, {} as Record<string, AggregatedResult>);

    const data = Object.values(aggregated).sort((a, b) =>
      a.period.localeCompare(b.period)
    );

    return {
      data,
      isEmpty: data.length === 0,
      total: data.reduce((sum, item) => sum + item.amount, 0),
    };
  }, [rawData, period]);
}
