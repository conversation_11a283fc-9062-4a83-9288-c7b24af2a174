import { formatCurrency } from "@/lib/utils";

// Common chart colors
export const CHART_COLORS = {
  primary: "hsl(var(--primary))",
  secondary: "hsl(var(--secondary))",
  success: "hsl(142.1, 76.2%, 36.3%)",
  danger: "hsl(346.8, 77.2%, 49.8%)",
  warning: "hsl(47.9, 95.8%, 53.1%)",
  info: "hsl(221.2, 83.2%, 53.3%)",
  muted: "hsl(var(--muted-foreground))",
};

// Color palettes for different chart types
export const COLOR_PALETTES = {
  default: ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"],
  financial: [
    CHART_COLORS.success,
    CHART_COLORS.danger,
    CHART_COLORS.warning,
    CHART_COLORS.info,
    CHART_COLORS.primary,
  ],
  investment: [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#8884D8",
    "#82ca9d",
  ],
  expense: [
    CHART_COLORS.danger,
    CHART_COLORS.warning,
    CHART_COLORS.info,
    CHART_COLORS.muted,
    CHART_COLORS.secondary,
  ],
};

// Common chart configurations
export const CHART_CONFIG = {
  margin: {
    default: { top: 10, right: 10, left: 0, bottom: 10 },
    withLegend: { top: 10, right: 30, left: 20, bottom: 5 },
  },
  grid: {
    strokeDasharray: "3 3",
    vertical: false,
    stroke: "#f0f0f0",
  },
  axis: {
    stroke: "#888888",
    fontSize: 12,
    tickLine: false,
    axisLine: false,
  },
  tooltip: {
    contentStyle: {
      borderRadius: "8px",
      boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
      border: "1px solid #e2e8f0",
      padding: "8px 12px",
    },
    cursor: { fill: "rgba(0, 0, 0, 0.04)" },
  },
  bar: {
    radius: [4, 4, 0, 0] as [number, number, number, number],
    barSize: 24,
    animationDuration: 1000,
  },
  line: {
    strokeWidth: 2,
    dot: true,
    activeDot: { r: 8 },
  },
  pie: {
    innerRadius: 0,
    outerRadius: 80,
    paddingAngle: 0,
  },
};

// Chart data transformation helpers
export interface MonthlyDataItem {
  month?: string;
  date?: string;
  [key: string]: unknown;
}

export const transformMonthlyData = (data: MonthlyDataItem[]) => {
  return data.map((item) => ({
    ...item,
    name: new Date(item.month || item.date || "").toLocaleDateString("en-US", {
      month: "short",
      year: "numeric",
    }),
  }));
};

export const transformCategoryData = (data: Record<string, number>) => {
  return Object.entries(data).map(([name, value]) => ({
    name,
    value,
  }));
};

export interface PerformanceDataItem {
  date: string;
  [key: string]: unknown;
}

export const transformPerformanceData = (data: PerformanceDataItem[]) => {
  return data.map((item) => ({
    ...item,
    name: new Date(item.date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    }),
  }));
};

// Common formatters
export const CHART_FORMATTERS = {
  currency: formatCurrency,
  percentage: (value: number) => `${value.toFixed(2)}%`,
  number: (value: number) => value.toLocaleString(),
  shortNumber: (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    }
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  },
  date: (value: string | Date) => {
    return new Date(value).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  },
};

// Legend helpers
export const createLegendItems = (
  data: Array<{ name: string; color: string }>
) => {
  return data.map((item) => ({
    color: item.color,
    label: item.name,
  }));
};

// Chart responsive helpers
export const getResponsiveHeight = (screenSize: "sm" | "md" | "lg" | "xl") => {
  const heights = {
    sm: 250,
    md: 300,
    lg: 350,
    xl: 400,
  };
  return heights[screenSize];
};

// Chart data type definitions
export interface ChartDataItem {
  [key: string]: unknown;
}

export interface PieDataItem {
  name: string;
  value: number;
  // [key: string]: string;
}

export interface LineDataItem {
  name: string;
  [key: string]: unknown;
}

// Common chart data validators
export const validateChartData = (data: ChartDataItem[]): boolean => {
  return Array.isArray(data) && data.length > 0;
};

export const validatePieData = (data: PieDataItem[]): boolean => {
  return (
    validateChartData(data) &&
    data.every((item) => item.name && typeof item.value === "number")
  );
};

export const validateLineData = (data: LineDataItem[]): boolean => {
  return (
    validateChartData(data) &&
    data.every((item) => item.name && Object.keys(item).length > 1)
  );
};

export const validateBarData = (data: LineDataItem[]): boolean => {
  return validateLineData(data);
};
