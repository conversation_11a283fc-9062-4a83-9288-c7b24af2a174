"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
} from "recharts";
import { BaseChart, ChartLoading, ChartEmpty } from "./BaseChart";

interface BarChartData {
  name: string;
  [key: string]: string | number;
}

interface BarConfig {
  dataKey: string;
  fill: string;
  name: string;
  radius?: [number, number, number, number];
  barSize?: number;
}

interface BarChartProps {
  title: string;
  description?: string;
  data: BarChartData[];
  bars: BarConfig[];
  isLoading?: boolean;
  error?: string;
  height?: number;
  className?: string;
  formatValue?: (value: number) => string;
  showLegend?: boolean;
  legendItems?: Array<{ color: string; label: string }>;
}

export function BarChart({
  title,
  description,
  data,
  bars,
  isLoading = false,
  error,
  height = 350,
  className = "",
  formatValue = (value) => value.toString(),
  showLegend = false,
  legendItems,
}: BarChartProps) {
  const renderChart = () => {
    if (isLoading) return <ChartLoading height={height} />;
    if (error) return <ChartEmpty height={height} message={error} />;
    if (!data || data.length === 0) return <ChartEmpty height={height} />;

    return (
      <div className="w-full">
        <ResponsiveContainer width="100%" height={height}>
          <RechartsBarChart
            data={data}
            margin={{ top: 10, right: 10, left: 0, bottom: 10 }}
          >
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              stroke="#f0f0f0"
            />
            <XAxis
              dataKey="name"
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              padding={{ left: 10, right: 10 }}
            />
            <YAxis
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={formatValue}
              width={60}
            />
            <Tooltip
              formatter={(value: number) => [formatValue(value), ""]}
              labelFormatter={(label) => `${label}`}
              contentStyle={{
                borderRadius: "8px",
                boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                border: "1px solid #e2e8f0",
                padding: "8px 12px",
              }}
              cursor={{ fill: "rgba(0, 0, 0, 0.04)" }}
            />
            {bars.map((bar) => (
              <Bar
                key={bar.dataKey}
                dataKey={bar.dataKey}
                fill={bar.fill}
                radius={bar.radius || [4, 4, 0, 0]}
                name={bar.name}
                barSize={bar.barSize || 24}
                animationDuration={1000}
              />
            ))}
          </RechartsBarChart>
        </ResponsiveContainer>
        {showLegend && legendItems && (
          <div className="flex justify-center gap-6 mt-2">
            {legendItems.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm font-medium">{item.label}</span>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseChart
      title={title}
      description={description}
      className={className}
      height={height}
    >
      {renderChart()}
    </BaseChart>
  );
}
