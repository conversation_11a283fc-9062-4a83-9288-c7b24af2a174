"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, Toolt<PERSON> } from "recharts";
import { BaseChart, ChartLoading, ChartEmpty } from "./BaseChart";
import { formatCurrency } from "@/shared/utils/formatters";

const CHART_COLORS = [
  "#8b5cf6", // purple
  "#f97316", // orange
  "#10b981", // green
  "#ef4444", // red
  "#3b82f6", // blue
  "#ec4899", // pink
  "#f59e0b", // amber
  "#14b8a6", // teal
  "#6b7280", // gray
  "#d946ef", // fuchsia
  "#0ea5e9", // sky
  "#84cc16", // lime
];

interface CategoryData {
  name: string;
  value: number;
}

interface ProcessedCategoryData extends CategoryData {
  color: string;
  percent: number;
  isHidden?: boolean;
}

interface ExpensesByCategoryChartProps {
  title?: string;
  description?: string;
  data: CategoryData[];
  isLoading?: boolean;
  error?: string;
  height?: number;
  className?: string;
  formatValue?: (value: number) => string;
  showLabels?: boolean;
  showLegend?: boolean;
  innerRadius?: number;
  outerRadius?: number;
}

const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  outerRadius,
  percent,
}: {
  cx: number;
  cy: number;
  midAngle: number;
  outerRadius: number;
  percent: number;
}) => {
  if (percent < 5) return null;

  const RADIAN = Math.PI / 180;
  const radius = outerRadius * 0.7;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? "start" : "end"}
      dominantBaseline="central"
      fontSize={12}
      fontWeight="bold"
    >
      {`${Math.round(percent)}%`}
    </text>
  );
};

const CustomTooltip = ({ 
  active, 
  payload, 
  formatValue = formatCurrency 
}: {
  active?: boolean;
  payload?: any[];
  formatValue?: (value: number) => string;
}) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    return (
      <div className="bg-background p-3 border rounded-lg shadow-lg">
        <p className="font-medium text-foreground">{data.name}</p>
        <p className="text-sm text-muted-foreground">
          {formatValue(Math.abs(data.value))}
        </p>
      </div>
    );
  }
  return null;
};

export function ExpensesByCategoryChart({
  title = "Expenses by Category",
  description,
  data,
  isLoading = false,
  error,
  height = 350,
  className = "",
  formatValue = formatCurrency,
  showLabels = true,
  showLegend = true,
  innerRadius = 60,
  outerRadius = 110,
}: ExpensesByCategoryChartProps) {
  const [chartData, setChartData] = useState<ProcessedCategoryData[]>([]);
  const [hiddenCategories, setHiddenCategories] = useState<string[]>([]);

  useEffect(() => {
    if (data && data.length > 0) {
      const filteredData = data.filter(
        (item) => !hiddenCategories.includes(item.name)
      );

      const total = filteredData.reduce(
        (sum, item) => sum + Math.abs(item.value),
        0
      );

      const sortedData = [...filteredData].sort(
        (a, b) => Math.abs(b.value) - Math.abs(a.value)
      );

      const formattedData = sortedData.map((item, index) => {
        const percent =
          total > 0 ? (Math.abs(item.value) / total) * 100 : 0;
        return {
          ...item,
          color: CHART_COLORS[index % CHART_COLORS.length],
          percent,
        };
      });

      const allCategoriesData = data
        .map((item) => {
          const isHidden = hiddenCategories.includes(item.name);
          const colorIndex =
            data.findIndex((cat) => cat.name === item.name) %
            CHART_COLORS.length;
          return {
            ...item,
            color: CHART_COLORS[colorIndex],
            isHidden,
            percent: isHidden
              ? 0
              : formattedData.find((d) => d.name === item.name)?.percent || 0,
          };
        })
        .sort((a, b) => Math.abs(b.value) - Math.abs(a.value));

      setChartData(allCategoriesData);
    } else {
      setChartData([]);
    }
  }, [data, hiddenCategories]);

  const toggleCategory = (categoryName: string) => {
    setHiddenCategories((prev) => {
      if (prev.includes(categoryName)) {
        return prev.filter((cat) => cat !== categoryName);
      } else {
        return [...prev, categoryName];
      }
    });
  };

  const renderChart = () => {
    if (isLoading) return <ChartLoading height={height} />;
    if (error) return <ChartEmpty height={height} message={error} />;
    if (!data || data.length === 0) {
      return <ChartEmpty height={height} message="No expense data available" />;
    }

    const visibleData = chartData.filter((item) => !item.isHidden);

    return (
      <div>
        <div style={{ height: height - (showLegend ? 120 : 0) }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={visibleData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={outerRadius}
                innerRadius={innerRadius}
                paddingAngle={2}
                fill="#8884d8"
                dataKey="value"
                label={showLabels ? renderCustomizedLabel : false}
                strokeWidth={2}
                stroke="hsl(var(--border))"
                minAngle={2}
              >
                {visibleData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip formatValue={formatValue} />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        {showLegend && chartData.length > 0 && (
          <div className="mt-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-x-4 gap-y-3 text-sm">
              {chartData.map((entry, index) => (
                <div
                  key={`legend-${index}`}
                  className={`flex items-center gap-2 cursor-pointer transition-opacity ${
                    entry.isHidden ? "opacity-50" : ""
                  }`}
                  onClick={() => toggleCategory(entry.name)}
                  title={
                    entry.isHidden
                      ? `Click to show ${entry.name}`
                      : `Click to hide ${entry.name}`
                  }
                >
                  <div
                    className={`w-3 h-3 rounded-full flex-shrink-0 ${
                      entry.isHidden ? "border border-dashed border-gray-400" : ""
                    }`}
                    style={{
                      backgroundColor: entry.isHidden ? "transparent" : entry.color,
                    }}
                  />
                  <div className="flex items-center gap-1.5 overflow-hidden">
                    <span
                      className={`font-medium whitespace-nowrap overflow-hidden text-ellipsis ${
                        entry.isHidden ? "line-through" : ""
                      }`}
                    >
                      {entry.name}
                    </span>
                    <span className="text-muted-foreground whitespace-nowrap">
                      {entry.isHidden ? "" : `${Math.round(entry.percent)}%`}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseChart
      title={title}
      description={description}
      className={className}
      height={height}
    >
      {renderChart()}
    </BaseChart>
  );
}
