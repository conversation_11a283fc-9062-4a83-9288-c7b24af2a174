/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts";
import { BaseChart, ChartLoading, ChartEmpty } from "./BaseChart";
import { COLOR_PALETTES } from "@/shared/constants/colors";

// Reuse existing PieDataItem from chartHelpers if available, otherwise define here
interface PieChartData {
  name: string;
  value: number;
  [key: string]: string | number;
}

// Extended interface for internal processing with toggle functionality
interface ProcessedPieChartData {
  name: string;
  value: number;
  color: string;
  percent: number;
  isHidden?: boolean;
}

interface PieChartProps {
  title: string;
  description?: string;
  data: PieChartData[];
  isLoading?: boolean;
  error?: string;
  height?: number;
  className?: string;
  colors?: string[];
  formatValue?: (value: number) => string;
  showLabels?: boolean;
  showLegend?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  // New interactive features
  allowToggle?: boolean;
  showPercentages?: boolean;
  interactiveLegend?: boolean;
  onToggleItem?: (itemName: string, isHidden: boolean) => void;
}

// Use expense palette for better colors, fallback to default
const DEFAULT_COLORS: string[] =
  COLOR_PALETTES.expense.length > 0
    ? [...COLOR_PALETTES.expense]
    : ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

const RADIAN = Math.PI / 180;

const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
}: {
  cx: number;
  cy: number;
  midAngle: number;
  innerRadius: number;
  outerRadius: number;
  percent: number;
}) => {
  // Only show label if percentage is significant enough
  if (percent < 0.05) return null;

  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? "start" : "end"}
      dominantBaseline="central"
      fontSize={12}
      fontWeight="bold"
    >
      {`${Math.round(percent * 100)}%`}
    </text>
  );
};

const CustomTooltip = ({
  active,
  payload,
  formatValue,
}: {
  active?: boolean;
  payload?: any[];
  formatValue?: (value: number) => string;
}) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    return (
      <div className="bg-background p-3 border rounded-lg shadow-lg">
        <p className="font-medium text-foreground">{data.name}</p>
        <p className="text-sm text-muted-foreground">
          {formatValue
            ? formatValue(Math.abs(data.value))
            : Math.abs(data.value)}
        </p>
      </div>
    );
  }
  return null;
};

export function PieChart({
  title,
  description,
  data,
  isLoading = false,
  error,
  height = 300,
  className = "",
  colors = DEFAULT_COLORS,
  formatValue,
  showLabels = true,
  showLegend = true,
  innerRadius = 0,
  outerRadius = 80,
  // New interactive features
  allowToggle = false,
  showPercentages = false,
  interactiveLegend = false,
  onToggleItem,
}: PieChartProps) {
  const [hiddenItems, setHiddenItems] = useState<string[]>([]);

  const processedData = useMemo((): ProcessedPieChartData[] => {
    if (!data || data.length === 0) return [];

    const visibleData = data.filter((item) => !hiddenItems.includes(item.name));

    const total = visibleData.reduce(
      (sum, item) => sum + Math.abs(item.value),
      0
    );

    // Process all data (including hidden items for legend)
    return data
      .map((item) => {
        const isHidden = hiddenItems.includes(item.name);
        const colorIndex =
          data.findIndex((d) => d.name === item.name) % colors.length;
        const percent =
          total > 0 && !isHidden ? (Math.abs(item.value) / total) * 100 : 0;

        return {
          name: item.name,
          value: item.value,
          color: colors[colorIndex],
          percent,
          isHidden,
        };
      })
      .sort((a, b) => Math.abs(b.value) - Math.abs(a.value));
  }, [data, hiddenItems, colors]);

  const visibleData = processedData.filter((item) => !item.isHidden);

  // Toggle item visibility
  const toggleItem = (itemName: string) => {
    setHiddenItems((prev) => {
      const newHiddenItems = prev.includes(itemName)
        ? prev.filter((name) => name !== itemName)
        : [...prev, itemName];

      // Call external callback if provided
      onToggleItem?.(itemName, !prev.includes(itemName));

      return newHiddenItems;
    });
  };

  const renderChart = () => {
    if (isLoading) return <ChartLoading height={height} />;
    if (error) return <ChartEmpty height={height} message={error} />;
    if (!data || data.length === 0) return <ChartEmpty height={height} />;

    const chartHeight = interactiveLegend ? height - 120 : height;

    return (
      <div>
        <div style={{ height: chartHeight }}>
          <ResponsiveContainer width="100%" height="100%">
            <RechartsPieChart>
              <Pie
                data={visibleData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={showLabels ? renderCustomizedLabel : false}
                outerRadius={outerRadius}
                innerRadius={innerRadius}
                fill="#8884d8"
                dataKey="value"
                paddingAngle={2}
                strokeWidth={2}
                stroke="hsl(var(--border))"
                minAngle={2}
              >
                {visibleData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip formatValue={formatValue} />} />
              {showLegend && !interactiveLegend && <Legend />}
            </RechartsPieChart>
          </ResponsiveContainer>
        </div>

        {interactiveLegend && processedData.length > 0 && (
          <div className="mt-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-x-4 gap-y-3 text-sm">
              {processedData.map((item, index) => (
                <div
                  key={`legend-${index}`}
                  className={`flex items-center gap-2 transition-opacity ${
                    allowToggle ? "cursor-pointer" : ""
                  } ${item.isHidden ? "opacity-50" : ""}`}
                  onClick={
                    allowToggle ? () => toggleItem(item.name) : undefined
                  }
                  title={
                    allowToggle
                      ? item.isHidden
                        ? `Click to show ${item.name}`
                        : `Click to hide ${item.name}`
                      : undefined
                  }
                >
                  <div
                    className={`w-3 h-3 rounded-full flex-shrink-0 ${
                      item.isHidden
                        ? "border border-dashed border-gray-400"
                        : ""
                    }`}
                    style={{
                      backgroundColor: item.isHidden
                        ? "transparent"
                        : item.color,
                    }}
                  />
                  <div className="flex items-center gap-1.5 overflow-hidden">
                    <span
                      className={`font-medium whitespace-nowrap overflow-hidden text-ellipsis ${
                        item.isHidden ? "line-through" : ""
                      }`}
                    >
                      {item.name}
                    </span>
                    {showPercentages && (
                      <span className="text-muted-foreground whitespace-nowrap">
                        {item.isHidden ? "" : `${Math.round(item.percent)}%`}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseChart
      title={title}
      description={description}
      className={className}
      height={height}
    >
      {renderChart()}
    </BaseChart>
  );
}
