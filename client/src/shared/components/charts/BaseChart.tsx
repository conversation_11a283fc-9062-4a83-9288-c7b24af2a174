"use client";

import { ReactNode } from "react";
import { Loader2 } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface BaseChartProps {
  title: string;
  description?: string;
  isLoading?: boolean;
  error?: string;
  children: ReactNode;
  className?: string;
  height?: number;
  loadingSkeleton?: ReactNode;
}

export function BaseChart({
  title,
  description,
  isLoading = false,
  error,
  children,
  className = "",
  height = 350,
  loadingSkeleton,
}: BaseChartProps) {
  const renderLoadingState = () => {
    if (loadingSkeleton) {
      return loadingSkeleton;
    }

    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <Skeleton className={`h-[${height}px] w-full rounded-lg`} />
        <div className="flex justify-center gap-6 mt-4">
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-24" />
        </div>
      </div>
    );
  };

  const renderErrorState = () => (
    <div
      className={`w-full h-[${height}px] flex items-center justify-center border rounded-lg`}
    >
      <p className="text-muted-foreground">
        {error || "Failed to load chart data. Please try again later."}
      </p>
    </div>
  );

  // const renderEmptyState = () => (
  //   <div className={`w-full h-[${height}px] flex items-center justify-center border rounded-lg`}>
  //     <p className="text-muted-foreground">No data available</p>
  //   </div>
  // );

  return (
    <Card className={`shadow-sm ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {isLoading
          ? renderLoadingState()
          : error
          ? renderErrorState()
          : children}
      </CardContent>
    </Card>
  );
}

interface ChartLoadingProps {
  height?: number;
}

export function ChartLoading({ height = 350 }: ChartLoadingProps) {
  return (
    <div className="flex justify-center items-center" style={{ height }}>
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
    </div>
  );
}

interface ChartEmptyProps {
  height?: number;
  message?: string;
}

export function ChartEmpty({
  height = 350,
  message = "No data available",
}: ChartEmptyProps) {
  return (
    <div
      className="flex justify-center items-center text-muted-foreground"
      style={{ height }}
    >
      {message}
    </div>
  );
}
