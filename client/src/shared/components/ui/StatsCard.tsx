"use client";

import { ReactNode } from "react";
import { Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface StatsCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  loading?: boolean;
  borderColor?: string;
  iconBgColor?: string;
  valueColor?: string;
  formatter?: (value: string | number) => string;
}

export function StatsCard({
  title,
  value,
  icon,
  loading = false,
  borderColor = "border-l-primary/70",
  iconBgColor = "bg-primary/10",
  valueColor = "",
  formatter,
}: StatsCardProps) {
  const displayValue = formatter ? formatter(value) : value;

  return (
    <Card className={`border-l-4 ${borderColor} shadow-sm`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon && (
          <div className={`h-8 w-8 rounded-full ${iconBgColor} flex items-center justify-center`}>
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className={`text-2xl font-bold ${valueColor}`}>
          {loading ? (
            <div className="flex items-center">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-muted-foreground">Loading...</span>
            </div>
          ) : (
            displayValue
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface StatsGridProps {
  cards: Array<{
    title: string;
    value: string | number;
    icon?: ReactNode;
    borderColor?: string;
    iconBgColor?: string;
    valueColor?: string;
    formatter?: (value: string | number) => string;
  }>;
  loading?: boolean;
  columns?: string;
}

export function StatsGrid({ 
  cards, 
  loading = false, 
  columns = "md:grid-cols-2 lg:grid-cols-4" 
}: StatsGridProps) {
  return (
    <div className={`grid gap-4 ${columns}`}>
      {cards.map((card, index) => (
        <StatsCard
          key={`${card.title}-${index}`}
          title={card.title}
          value={card.value}
          icon={card.icon}
          loading={loading}
          borderColor={card.borderColor}
          iconBgColor={card.iconBgColor}
          valueColor={card.valueColor}
          formatter={card.formatter}
        />
      ))}
    </div>
  );
}
