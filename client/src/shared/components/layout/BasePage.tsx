"use client";

import { ReactNode } from "react";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface BasePageProps {
  title: string;
  isLoading: boolean;
  error: string | null;
  onRetry: () => void;
  headerActions?: ReactNode;
  tabs: {
    value: string;
    label: string;
    content: ReactNode;
  }[];
  defaultTab?: string;
  children?: ReactNode;
  floatingActionButton?: ReactNode;
  loadingMessage?: string;
}

export function BasePage({
  title,
  isLoading,
  error,
  onRetry,
  headerActions,
  tabs,
  defaultTab,
  children,
  floatingActionButton,
  loadingMessage = "Loading your data...",
}: BasePageProps) {
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-lg text-muted-foreground">{loadingMessage}</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="text-center max-w-md p-6 border rounded-lg shadow-sm">
          <h2 className="text-2xl font-bold text-rose-500 mb-2">
            Error Loading Data
          </h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={onRetry}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between space-y-2 md:space-y-0">
          <h2 className="text-3xl font-bold tracking-tight">{title}</h2>
          {headerActions && (
            <div className="flex items-center space-x-2">{headerActions}</div>
          )}
        </div>

        {children}

        <Tabs defaultValue={defaultTab || tabs[0]?.value} className="space-y-6">
          <TabsList className="w-full md:w-auto">
            {tabs.map((tab) => (
              <TabsTrigger key={tab.value} value={tab.value}>
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {tabs.map((tab) => (
            <TabsContent key={tab.value} value={tab.value} className="space-y-4">
              {tab.content}
            </TabsContent>
          ))}
        </Tabs>
      </div>

      {floatingActionButton && (
        <div className="fixed right-4 bottom-4 md:hidden z-10">
          {floatingActionButton}
        </div>
      )}
    </div>
  );
}
