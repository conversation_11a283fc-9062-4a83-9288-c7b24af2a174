"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, ResponsiveC<PERSON>r, Toolt<PERSON> } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";

const colors = [
  "#8b5cf6", // purple
  "#f97316", // orange
  "#10b981", // green
  "#ef4444", // red
  "#3b82f6", // blue
  "#ec4899", // pink
  "#f59e0b", // amber
  "#14b8a6", // teal
  "#6b7280", // gray
  "#d946ef", // fuchsia
  "#0ea5e9", // sky
  "#84cc16", // lime
];

const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  outerRadius,
  percent,
}: {
  cx: number;
  cy: number;
  midAngle: number;
  outerRadius: number;
  percent: number;
}) => {
  if (percent < 5) return null;

  const RADIAN = Math.PI / 180;
  const radius = outerRadius * 0.7;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? "start" : "end"}
      dominantBaseline="central"
      fontSize={12}
      fontWeight="bold"
    >
      {`${percent}%`}
    </text>
  );
};

interface ExpensesByCategoryChartProps {
  categoryData?: Array<{
    name: string;
    value: number;
  }>;
  isLoading?: boolean;
}

export function ExpensesByCategoryChart({
  categoryData,
  isLoading = false,
}: ExpensesByCategoryChartProps) {
  const [chartData, setChartData] = useState([]);
  const [hiddenCategories, setHiddenCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(isLoading);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (categoryData && categoryData.length > 0) {
      const filteredData = categoryData.filter(
        (item) => !hiddenCategories.includes(item.name)
      );

      const total = filteredData.reduce(
        (sum, item) => sum + Math.abs(item.value),
        0
      );

      const sortedData = [...filteredData].sort(
        (a, b) => Math.abs(b.value) - Math.abs(a.value)
      );

      const formattedData = sortedData.map((item, index) => {
        const percent =
          total > 0 ? Math.round((Math.abs(item.value) / total) * 100) : 0;
        return {
          ...item,
          color: colors[index % colors.length],
          percent,
        };
      });

      const allCategoriesData = categoryData
        .map((item) => {
          const isHidden = hiddenCategories.includes(item.name);
          const colorIndex =
            categoryData.findIndex((cat) => cat.name === item.name) %
            colors.length;
          return {
            ...item,
            color: colors[colorIndex],
            isHidden,
            percent: isHidden
              ? 0
              : formattedData.find((d) => d.name === item.name)?.percent || 0,
          };
        })
        .sort((a, b) => Math.abs(b.value) - Math.abs(a.value));

      setChartData(allCategoriesData);
      setLoading(false);
      setError(false);
    } else if (categoryData && categoryData.length === 0) {
      setChartData([]);
      setLoading(false);
      setError(false);
    } else {
      // If no data provided, use fallback
      setChartData([]);
      setLoading(false);
    }
  }, [categoryData, hiddenCategories]);

  if (loading) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-6">
        <Skeleton className="h-[250px] w-full rounded-full" />
        <div className="mt-4 w-full">
          <Skeleton className="h-6 w-full rounded-lg mb-2" />
          <Skeleton className="h-6 w-3/4 rounded-lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-[250px] flex items-center justify-center border rounded-lg">
        <p className="text-muted-foreground">
          Failed to load category data. Please try again later.
        </p>
      </div>
    );
  }

  if (chartData.length === 0) {
    return (
      <div className="w-full h-[250px] flex items-center justify-center border rounded-lg">
        <p className="text-muted-foreground">
          No expense data available. Add some expenses to see your spending
          breakdown.
        </p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={chartData.filter((item) => !item.isHidden)}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={110}
            innerRadius={60}
            paddingAngle={2}
            fill="#8884d8"
            dataKey="value"
            label={renderCustomizedLabel}
            strokeWidth={2}
            stroke="#1e1e1e"
            minAngle={2}
          >
            {chartData
              .filter((item) => !item.isHidden)
              .map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
          </Pie>
          <Tooltip
            formatter={(value) => [
              `${formatCurrency(Math.abs(value as number))}`,
              "Amount",
            ]}
            contentStyle={{
              borderRadius: "8px",
              boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
              border: "1px solid #e2e8f0",
              color: "#ffffff",
            }}
          />
        </PieChart>
      </ResponsiveContainer>
      <div className="mt-4">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-x-4 gap-y-3 text-sm">
          {chartData.map((entry, index) => (
            <div
              key={`legend-${index}`}
              className={`flex items-center gap-2 cursor-pointer ${
                entry.isHidden ? "opacity-50" : ""
              }`}
              onClick={() => {
                setHiddenCategories((prev) => {
                  if (prev.includes(entry.name)) {
                    return prev.filter((cat) => cat !== entry.name);
                  } else {
                    return [...prev, entry.name];
                  }
                });
              }}
              title={
                entry.isHidden
                  ? `Click to show ${entry.name}`
                  : `Click to hide ${entry.name}`
              }
            >
              <div
                className={`w-3 h-3 rounded-full flex-shrink-0 ${
                  entry.isHidden ? "border border-dashed border-gray-400" : ""
                }`}
                style={{
                  backgroundColor: entry.isHidden ? "transparent" : entry.color,
                }}
              />
              <div className="flex items-center gap-1.5 overflow-hidden">
                <span
                  className={`font-medium whitespace-nowrap overflow-hidden text-ellipsis ${
                    entry.isHidden ? "line-through" : ""
                  }`}
                >
                  {entry.name}
                </span>
                <span className="text-muted-foreground whitespace-nowrap">
                  {entry.isHidden ? "" : `${entry.percent}%`}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
