import api from "./api";

export enum INVESTMENT_TYPE {
  STOCK = "STOCK",
  MUTUAL_FUND = "MUTUAL_FUND",
  GOLD = "GOLD",
  SILVER = "SILVER",
  CURRENCY = "CURRENCY",
}

export enum INVESTMENT_PURPOSE {
  MONITORING = "MONITORING",
  OWNED = "OWNED",
}

export interface Purchase {
  price?: number;
  quantity?: number;
}

export interface Investment {
  _id?: string;
  name: string;
  symbol: string;
  // Legacy fields kept for backward compatibility
  purchasedPrice?: number;
  quantity?: number;
  // New field for multiple purchases
  purchases?: Purchase[];
  type: INVESTMENT_TYPE;
  purpose: INVESTMENT_PURPOSE;
  targetPrice?: number;
  userId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // New fields for owned stocks
  currentPrice?: number;
  profitPerShare?: number;
  totalProfit?: number;
  // Calculated fields from purchases array
  avgPurchasePrice?: number;
  totalQuantity?: number;
}

export interface InvestmentStats {
  totalInvestment: number;
  totalCurrentValue: number;
  totalReturn: number;
  returnPercentage: number;
  investmentsByType: {
    name: string;
    value: number;
  }[];
  recentInvestments: Investment[];
  performanceData: {
    name: string;
    value: number;
  }[];
}

export interface InvestmentFilters {
  type?: INVESTMENT_TYPE;
  purpose?: INVESTMENT_PURPOSE;
  searchQuery?: string;
  sortBy?: string;
  sortDirection?: "asc" | "desc";
}

// Get all investments
export const getInvestments = async (
  filters?: InvestmentFilters
): Promise<Investment[]> => {
  let queryParams = "";

  if (filters) {
    const params = new URLSearchParams();

    if (filters.type) {
      params.append("type", filters.type);
    }

    if (filters.purpose) {
      params.append("purpose", filters.purpose);
    }

    if (filters.searchQuery) {
      params.append("search", filters.searchQuery);
    }

    if (filters.sortBy) {
      params.append("sortBy", filters.sortBy);
      params.append("sortDirection", filters.sortDirection || "desc");
    }

    queryParams = `?${params.toString()}`;
  }

  const response = await api.get(`/investments${queryParams}`);
  return response.data.data;
};

// Get investment by ID
export const getInvestmentById = async (id: string): Promise<Investment> => {
  const response = await api.get(`/investments/${id}`);
  return response.data.data;
};

// Create new investment
export const createInvestment = async (
  investment: Investment
): Promise<Investment> => {
  const response = await api.post("/investments", investment);
  return response.data.data;
};

// Update investment
export const updateInvestment = async (
  id: string,
  investment: Investment
): Promise<Investment> => {
  const response = await api.put(`/investments/${id}`, investment);
  return response.data.data;
};

// Delete investment
export const deleteInvestment = async (id: string): Promise<void> => {
  await api.delete(`/investments/${id}`);
};

// Get investment statistics for dashboard
export const getInvestmentStats = async (): Promise<InvestmentStats> => {
  const response = await api.get("/investments/stats");
  return response.data.data;
};

export interface StockDataType {
  name: string;
  data: [number, number][];
  purpose?: INVESTMENT_PURPOSE;
}
// Get price tracking data
export const getPriceTracking = async (
  type: string
): Promise<StockDataType[]> => {
  const response = await api.get(`/investments/price-tracking?type=${type}`);
  return response.data;
};
