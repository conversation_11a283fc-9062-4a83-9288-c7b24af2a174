import api from "./api";

export interface Expense {
  _id?: string;
  type: EXPENSE_TYPE;
  name: string;
  note?: string;
  amount: number;
  category: string;
  eventDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum EXPENSE_TYPE {
  INCOME = "INCOME",
  EXPENSE = "EXPENSE",
  DEBT_BOUGHT = "DEBT_BOUGHT",
  DEBT_GIVEN = "DEBT_GIVEN",
  INVESTMENT = "INVESTMENT",
  INCOME_TAX = "INCOME_TAX",
}

export const EXPENSE_MAP = {
  INCOME: "Income",
  EXPENSE: "Expenses",
  DEBT_BOUGHT: "Debt Bought",
  DEBT_GIVEN: "Debt Given",
};

export interface ExpenseStats {
  groupedExpense: Record<EXPENSE_TYPE, number>;
  expensesByCategory: {
    name: string;
    value: number;
  }[];
  monthlyData: {
    name: string;
    income: number;
    expenses: number;
  }[];
  recentTransactions: Expense[];
}

export interface ExpenseFilters {
  type?: string;
  categories?: string[];
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  searchQuery?: string;
  sortBy?: string;
  sortDirection?: "asc" | "desc";
  ignoreDate?: boolean;
}

// Get all expenses
export const getExpenses = async (
  filters?: ExpenseFilters
): Promise<Expense[]> => {
  let queryParams = "";

  if (filters) {
    const params = new URLSearchParams();

    if (filters.type && filters.type !== "all") {
      params.append("type", filters.type);
    }

    if (filters.categories && filters.categories.length > 0) {
      params.append("categories", filters.categories.join(","));
    }

    // Add ignoreDate parameter if true
    if (filters.ignoreDate) {
      params.append("ignoreDate", "true");
    } else {
      // Only add date parameters if we're not ignoring dates
      if (filters.dateRange?.from) {
        params.append("fromDate", filters.dateRange.from.toISOString());
      }

      if (filters.dateRange?.to) {
        params.append("toDate", filters.dateRange.to.toISOString());
      }
    }

    if (filters.searchQuery) {
      params.append("search", filters.searchQuery);
    }

    if (filters.sortBy) {
      params.append("sortBy", filters.sortBy);
      params.append("sortDirection", filters.sortDirection || "desc");
    }

    queryParams = `?${params.toString()}`;
  }

  const response = await api.get(`/expenses${queryParams}`);
  return response.data.data;
};

// Get expense by ID
export const getExpenseById = async (id: string): Promise<Expense> => {
  const response = await api.get(`/expenses/${id}`);
  return response.data.data;
};

// Create new expense
export const createExpense = async (expense: Expense): Promise<Expense> => {
  const response = await api.post("/expenses", expense);
  return response.data.data;
};

// Update expense
export const updateExpense = async (
  id: string,
  expense: Expense
): Promise<Expense> => {
  const response = await api.put(`/expenses/${id}`, expense);
  return response.data.data;
};

// Delete expense
export const deleteExpense = async (id: string): Promise<void> => {
  await api.delete(`/expenses/${id}`);
};

// Get expense statistics for dashboard
export const getExpenseStats = async (
  dateRange?: {
    from?: Date;
    to?: Date;
  },
  ignoreDate?: boolean
): Promise<ExpenseStats> => {
  let queryParams = "";
  const params = new URLSearchParams();

  // Add ignoreDate parameter if true
  if (ignoreDate) {
    params.append("ignoreDate", "true");
  } else if (dateRange) {
    // Only add date parameters if we're not ignoring dates
    if (dateRange.from) {
      params.append("fromDate", dateRange.from.toISOString());
    }

    if (dateRange.to) {
      params.append("toDate", dateRange.to.toISOString());
    }
  }

  if (params.toString()) {
    queryParams = `?${params.toString()}`;
  }

  const response = await api.get(`/expenses/stats${queryParams}`);
  return response.data.data;
};
