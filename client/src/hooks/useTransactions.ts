import { useState, useEffect } from "react";
import {
  getExpenses,
  Expense,
  ExpenseFilters,
} from "@/services/expense.service";
import { useExpense } from "@/contexts/ExpenseContext";
import { useToast } from "@/components/ui/use-toast";

interface UseTransactionsProps {
  filters: {
    type: string;
    categories: string[];
    dateRange: {
      from: Date | undefined;
      to: Date | undefined;
    };
    searchQuery: string;
    sortBy: string;
    sortDirection: "asc" | "desc";
    ignoreDate: boolean;
  };
}

export function useTransactions({ filters }: UseTransactionsProps) {
  const { refreshTrigger } = useExpense();
  const [isLoading, setIsLoading] = useState(true);
  const [transactions, setTransactions] = useState<Expense[]>([]);
  const { toast } = useToast();

  // Fetch transactions from API
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setIsLoading(true);

        // Convert filters to API format
        const apiFilters: ExpenseFilters = {
          type: filters.type,
          categories: filters.categories,
          ignoreDate: filters.ignoreDate,
          dateRange: {
            from: filters.dateRange.from,
            to: filters.dateRange.to,
          },
          searchQuery: filters.searchQuery,
          sortBy: filters.sortBy,
          sortDirection: filters.sortDirection,
        };

        const data = await getExpenses(apiFilters);
        setTransactions(data);
      } catch (err) {
        console.error("Failed to fetch transactions:", err);
        toast({
          title: "Error",
          description: "Failed to load transactions. Please try again.",
          variant: "destructive",
        });
        setTransactions([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, [filters, refreshTrigger, toast]);

  return {
    transactions,
    isLoading,
    setTransactions,
    setIsLoading,
  };
}
