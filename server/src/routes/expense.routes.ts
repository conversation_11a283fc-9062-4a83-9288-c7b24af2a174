import express from 'express';
import {
  getExpenses,
  getExpense,
  createExpense,
  updateExpense,
  deleteExpense,
  getExpenseStats,
} from '../controllers/expense.controller';
import { protect } from '../middleware/auth';
import { validate } from '../middleware/validate';
import { body } from 'express-validator';

const router = express.Router();

const expenseValidator = [
  body('type')
    .notEmpty()
    .withMessage('Type is required')
    .isIn(['income', 'expense', 'debt_bought', 'debt_given', 'investment', 'tax_paid'])
    .withMessage('Invalid expense type'),
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isNumeric()
    .withMessage('Amount must be a number'),
  body('category').notEmpty().withMessage('Category is required'),
  body('eventDate').optional().isISO8601().withMessage('Invalid date format'),
];

router.use(protect);

router.get('/stats', getExpenseStats);

router.route('/').get(getExpenses).post(validate(expenseValidator), createExpense);

router
  .route('/:id')
  .get(getExpense)
  .put(validate(expenseValidator), updateExpense)
  .delete(deleteExpense);

export default router;
