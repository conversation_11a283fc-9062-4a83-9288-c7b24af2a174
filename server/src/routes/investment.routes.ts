import express from 'express';
import {
  getPriceTracking,
  getInvestments,
  getInvestment,
  createInvestment,
  updateInvestment,
  deleteInvestment,
  getInvestmentStats,
} from '../controllers/investment.controller';
import { protect } from '../middleware/auth';
import { validate } from '../middleware/validate';
import { body } from 'express-validator';

const router = express.Router();

router.use(protect);

const investmentValidator = [
  body('name').notEmpty().withMessage('Name is required'),
  body('symbol').notEmpty().withMessage('Symbol is required'),
  body('price').optional().isNumeric().withMessage('Price must be a number'),
  body('type')
    .notEmpty()
    .withMessage('Type is required')
    .isIn(['STOCK', 'MUTUAL_FUND', 'GOLD', 'SILVER'])

    .withMessage('Invalid investment type'),
  body('purpose')
    .notEmpty()
    .withMessage('Purpose is required')
    .isIn(['MONITORING', 'OWNED'])
    .withMessage('Invalid purpose'),
  body('quantity').optional().isNumeric().withMessage('Quantity must be a number'),
  body('targetPrice').optional().isNumeric().withMessage('Target price must be a number'),
];

router.get('/stats', getInvestmentStats);

router.get('/price-tracking', getPriceTracking);

router.route('/').get(getInvestments).post(validate(investmentValidator), createInvestment);

router
  .route('/:id')
  .get(getInvestment)
  .put(validate(investmentValidator), updateInvestment)
  .delete(deleteInvestment);

export default router;
