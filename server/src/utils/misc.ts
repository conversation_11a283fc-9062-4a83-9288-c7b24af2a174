import { StockData } from '@/lib/investments';

export function convertToStockFormat<T extends string>(
  name: string,
  key: T,
  inputArray: any,
): StockData {
  const result = {
    name: name.toUpperCase(),
    data: inputArray.map((item: any) => {
      const timestamp = new Date(item.date).getTime();
      const value = parseFloat(item[key]);

      return [timestamp, value];
    }),
  };

  return result;
}

// "22-04-2025", -> convert this to new Date()

export function convertDateFormat(inputDate: string): string {
  const [day, month, year] = inputDate.split('-');
  return new Date(`${year}-${month}-${day}`).toISOString();
}

export function convertToStockFormatGoldSilver(
  name: string,
  key: string,
  inputArray: Record<string, string>[],
) {
  const result = {
    name: name.toUpperCase(),
    data: inputArray.map((item) => {
      const timestamp = new Date(convertDateFormat(item.date)).getTime();
      const value = parseFloat(item[key]);

      return [timestamp, value];
    }),
  };

  return result;
}
