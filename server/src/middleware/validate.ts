import { Request, Response, NextFunction } from 'express';
import { validationR<PERSON>ult, Validation<PERSON>hain } from 'express-validator';
import { AppError } from './errorHandler';

export const validate = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    await Promise.all(validations.map((validation) => validation.run(req)));

    const errors = validationResult(req);
    if (errors.isEmpty()) {
      return next();
    }

    const error: AppError = new Error('Validation failed');
    error.statusCode = 400;
    error.errors = errors.array();
    next(error);
  };
};
