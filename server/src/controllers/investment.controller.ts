import { Request, Response } from 'express';
import { Investments } from '../models/investment.model';
import { getInvestmentStrategy, StockStrategy, InvestmentType } from '../lib/investments';

export const getInvestments = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { type, purpose, search, sortBy, sortDirection } = req.query;

    const filter: any = { userId: req.user?.id };

    if (type) {
      filter.type = type;
    }

    if (purpose) {
      filter.purpose = purpose;
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { symbol: { $regex: search, $options: 'i' } },
      ];
    }

    // Build sort object
    const sort: any = {};
    if (sortBy) {
      sort[sortBy as string] = sortDirection === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by creation date, newest first
    }

    const investments = await Investments.find(filter).sort(sort);

    return res.status(200).json({
      success: true,
      count: investments.length,
      data: investments,
    });
  } catch (error) {
    console.error('Error fetching investments:', error);
    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

// Get single investment
export const getInvestment = async (req: Request, res: Response): Promise<Response> => {
  try {
    const investment = await Investments.findOne({
      _id: req.params.id,
      userId: req.user?.id,
    });

    if (!investment) {
      return res.status(404).json({
        success: false,
        error: 'Investment not found',
      });
    }

    return res.status(200).json({
      success: true,
      data: investment,
    });
  } catch (error) {
    console.error('Error fetching investment:', error);
    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

export const createInvestment = async (req: Request, res: Response): Promise<Response> => {
  try {
    // Add user ID to request body
    req.body.userId = req.user?.id;

    const investment = await Investments.create(req.body);

    return res.status(201).json({
      success: true,
      data: investment,
    });
  } catch (error) {
    console.error('Error creating investment:', error);

    if ((error as any).name === 'ValidationError') {
      const messages = Object.values((error as any).errors).map((val: any) => val.message);
      return res.status(400).json({
        success: false,
        error: messages,
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

// Update investment
export const updateInvestment = async (req: Request, res: Response): Promise<Response> => {
  try {
    let investment = await Investments.findOne({
      _id: req.params.id,
      userId: req.user?.id,
    });

    if (!investment) {
      return res.status(404).json({
        success: false,
        error: 'Investment not found',
      });
    }

    investment = await Investments.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    return res.status(200).json({
      success: true,
      data: investment,
    });
  } catch (error) {
    console.error('Error updating investment:', error);

    if ((error as any).name === 'ValidationError') {
      const messages = Object.values((error as any).errors).map((val: any) => val.message);
      return res.status(400).json({
        success: false,
        error: messages,
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

// Delete investment
export const deleteInvestment = async (req: Request, res: Response): Promise<Response> => {
  try {
    const investment = await Investments.findOne({
      _id: req.params.id,
      userId: req.user?.id,
    });

    if (!investment) {
      return res.status(404).json({
        success: false,
        error: 'Investment not found',
      });
    }

    await investment.deleteOne();

    return res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Error deleting investment:', error);
    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

/**
 *
 * This endpoint calculates and returns detailed portfolio analytics including:
 * - Total investment amount and current value
 * - Profit/loss calculations with real-time stock prices
 * - Investment distribution by type (stocks, mutual funds, etc.)
 * - Individual stock performance metrics
 * - Recent investments sorted by creation date
 *
 * @route GET /api/investments/stats
 * @access Private (requires authentication)
 *
 * @param {Request} req - Express request object containing authenticated user info
 * @param {Response} res - Express response object
 *
 * @returns {Promise<Response>} JSON response containing investment statistics
 *
 * @example
 * // Response format:
 * {
 *   "success": true,
 *   "data": {
 *     "totalInvestment": 50000,
 *     "totalCurrentValue": 55000,
 *     "totalReturn": 5000,
 *     "returnPercentage": 10.0,
 *     "investmentsByType": [
 *       { "name": "STOCK", "value": 45000 },
 *       { "name": "MUTUAL_FUND", "value": 10000 }
 *     ],
 *     "recentInvestments": [...],
 *     "performanceData": [...]
 *   }
 * }
 *
 * @description
 * Algorithm Flow:
 * 1. Fetches all user investments from database
 * 2. Filters owned stocks for real-time price analysis
 * 3. Uses Stock Strategy to fetch current market prices
 * 4. Enhances each investment with calculated fields:
 *    - Average purchase price (from multiple purchases)
 *    - Current market price (for stocks)
 *    - Profit/loss per share and total
 *    - Total quantity across all purchases
 * 5. Calculates portfolio-level metrics:
 *    - Total investment amount
 *    - Current portfolio value
 *    - Absolute and percentage returns
 * 6. Aggregates investments by type for distribution analysis
 * 7. Creates performance data for individual stocks
 *
 * @features
 * - Multi-purchase support with weighted average calculations
 * - Real-time stock price integration via Strategy pattern
 * - Backward compatibility with legacy single-purchase format
 * - Comprehensive profit/loss analysis
 * - Investment type aggregation (STOCK, MUTUAL_FUND, GOLD, SILVER)
 * - Purpose-based filtering (OWNED vs MONITORING)
 *
 * @throws {500} Server Error - Database connection issues or external API failures
 * @throws {401} Unauthorized - Missing or invalid authentication
 */
export const getInvestmentStats = async (req: Request, res: Response): Promise<Response> => {
  try {
    const allInvestments = await Investments.find({ userId: req.user.id });

    const ownedStocks = allInvestments.filter(
      (investment) => investment.purpose === 'OWNED' && investment.type === 'STOCK',
    );

    const stockSymbols: Record<string, string> = {};

    ownedStocks.forEach((stock) => {
      stockSymbols[stock.name] = stock.symbol;
    });

    const stockStrategy = getInvestmentStrategy(InvestmentType.STOCK) as StockStrategy;

    const stockResponses = await stockStrategy.fetchData(stockSymbols);

    const currentPrices: Record<string, number> = {};

    stockResponses.forEach((stockData) => {
      // Get the latest price from the data array (last element)
      if (stockData.data && stockData.data.length > 0) {
        const latestDataPoint = stockData.data[stockData.data.length - 1];
        currentPrices[stockData.name] = latestDataPoint[1];
      }
    });

    // Calculate investment statistics
    let totalInvestment = 0;
    let totalCurrentValue = 0;

    // Process each owned stock and add current price and profit information
    const enhancedInvestments = allInvestments.map((investment) => {
      // Create a copy of the investment object that we can modify
      const enhancedInvestment = investment.toObject();

      // For owned stocks, add current price and profit information
      if (investment.purpose === 'OWNED' && investment.type === 'STOCK') {
        // Check if we have purchases array or legacy fields
        const purchases =
          investment.purchases && investment.purchases.length > 0
            ? investment.purchases
            : investment.purchasedPrice && investment.quantity
            ? [{ price: investment.purchasedPrice, quantity: investment.quantity }]
            : [];

        if (purchases.length > 0) {
          let investmentValue = 0;
          let totalQuantity = 0;

          // Calculate total investment value and quantity from all purchases
          purchases.forEach((purchase: { price: number; quantity: number }) => {
            investmentValue += purchase.price * purchase.quantity;
            totalQuantity += purchase.quantity;
          });

          // Calculate average purchase price
          const avgPurchasePrice = investmentValue / totalQuantity;

          // Add current price if available
          if (currentPrices[investment.name]) {
            const currentPrice = currentPrices[investment.name];
            enhancedInvestment.currentPrice = currentPrice;

            // Calculate profit/loss per share based on average purchase price
            const profitPerShare = currentPrice - avgPurchasePrice;
            enhancedInvestment.profitPerShare = profitPerShare;

            // Calculate total profit/loss
            const currentValue = currentPrice * totalQuantity;
            // Profit = Current Value - Investment Value
            const totalProfit = currentValue - investmentValue;
            enhancedInvestment.totalProfit = totalProfit;

            totalCurrentValue += currentValue;
          } else {
            // If we don't have current price, use the average purchase price as fallback
            enhancedInvestment.currentPrice = avgPurchasePrice;
            enhancedInvestment.profitPerShare = 0;
            enhancedInvestment.totalProfit = 0;
            totalCurrentValue += investmentValue;
          }

          // Add total investment value
          totalInvestment += investmentValue;

          // Add calculated values to the enhanced investment
          enhancedInvestment.avgPurchasePrice = avgPurchasePrice;
          enhancedInvestment.totalQuantity = totalQuantity;
        }
      } else if (investment.purpose === 'OWNED') {
        // For non-stock investments that are owned
        // Check if we have purchases array or legacy fields
        const purchases =
          investment.purchases && investment.purchases.length > 0
            ? investment.purchases
            : investment.purchasedPrice && investment.quantity
            ? [{ price: investment.purchasedPrice, quantity: investment.quantity }]
            : [];

        if (purchases.length > 0) {
          let investmentValue = 0;
          let totalQuantity = 0;

          // Calculate total investment value and quantity from all purchases
          purchases.forEach((purchase: { price: number; quantity: number }) => {
            investmentValue += purchase.price * purchase.quantity;
            totalQuantity += purchase.quantity;
          });

          totalInvestment += investmentValue;
          totalCurrentValue += investmentValue;

          // Add calculated values to the enhanced investment
          enhancedInvestment.avgPurchasePrice = investmentValue / totalQuantity;
          enhancedInvestment.totalQuantity = totalQuantity;
        }
      }

      return enhancedInvestment;
    });

    // Calculate return and percentage
    const totalReturn = totalCurrentValue - totalInvestment;
    const returnPercentage = totalInvestment > 0 ? (totalReturn / totalInvestment) * 100 : 0;

    // Group investments by type
    const investmentsByType = enhancedInvestments.reduce((acc: any[], investment) => {
      if (investment.purpose === 'OWNED') {
        // Only process investments that have either totalQuantity or quantity
        if (investment.totalQuantity || investment.quantity) {
          const existingType = acc.find((item) => item.name === investment.type);

          // Calculate the value based on current price for stocks, or average purchase price for others
          let value = 0;

          if (investment.type === 'STOCK' && investment.currentPrice) {
            // Use totalQuantity if available (from purchases array), otherwise fall back to quantity
            const quantity = investment.totalQuantity || investment.quantity;
            value = investment.currentPrice * quantity;
          } else if (investment.avgPurchasePrice && investment.totalQuantity) {
            // Use calculated average price and total quantity from purchases
            value = investment.avgPurchasePrice * investment.totalQuantity;
          } else if (investment.purchasedPrice && investment.quantity) {
            // Fall back to legacy fields
            value = investment.purchasedPrice * investment.quantity;
          }

          if (existingType) {
            existingType.value += value;
          } else {
            acc.push({
              name: investment.type,
              value: value,
            });
          }
        }
      }
      return acc;
    }, []);

    // Sort investments by creation date (newest first)
    enhancedInvestments.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    );

    // Create performance data (could be expanded in the future)
    const performanceData = enhancedInvestments
      .filter(
        (stock) =>
          stock.purpose === 'OWNED' &&
          stock.type === 'STOCK' &&
          (stock.totalQuantity || stock.quantity),
      )
      .map((stock) => {
        return {
          name: stock.name,
          value: stock.totalProfit || 0,
        };
      });

    return res.status(200).json({
      success: true,
      data: {
        totalInvestment,
        totalCurrentValue,
        totalReturn,
        returnPercentage,
        investmentsByType,
        recentInvestments: enhancedInvestments,
        performanceData,
      },
    });
  } catch (error) {
    console.error('Error fetching investment stats:', error);
    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

export const getPriceTracking = async (req: Request, res: Response): Promise<Response> => {
  const type = req.query.type as string;

  const strategy = getInvestmentStrategy(type);

  if (!strategy) {
    const validTypes = Object.values(InvestmentType).join(', ');
    return res.status(400).json({
      error: `Invalid type. Use one of: ${validTypes}`,
    });
  }

  try {
    if (strategy.isScraper) {
      // For scraper strategies (gold, silver, currency), just fetch data directly
      const scrapedData = await strategy.fetchData();
      return res.status(200).json(scrapedData);
    }

    // For data strategies (mutual_fund, stock), get user's investments first
    const combinedInvestment: Record<string, string> = {};

    const investments = await Investments.find({
      userId: req.user.id,
      type: type.toUpperCase(),
      // purpose: 'OWNED',
    });

    investments.forEach((investment) => {
      combinedInvestment[investment.name] = investment.symbol;
    });

    // Use the strategy's fetchData method with the user's investments
    const allResponses = await strategy.fetchData(combinedInvestment);
    return res.status(200).json(allResponses);
  } catch (error) {
    console.error(`[${type.toUpperCase()}] Fetch error:`, error);
    return res.status(500).json({ error: 'Failed to fetch price data' });
  }
};
