import { Request, Response, NextFunction } from 'express';
import { Expense, IExpense } from '../models/expense.model';
import { AppError } from '../middleware/errorHandler';

export const getExpenses = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { type, categories, fromDate, toDate, search, sortBy, sortDirection, ignoreDate } =
      req.query;

    const filter: any = { userId: req.user.id };

    if (type && type !== 'all') {
      filter.type = (type as string).toUpperCase();
    }

    if (categories && categories !== '') {
      filter.category = { $in: (categories as string).split(',') };
    }

    if ((fromDate || toDate) && ignoreDate !== 'true') {
      filter.eventDate = {};
      if (fromDate) {
        filter.eventDate.$gte = new Date(fromDate as string);
      }
      if (toDate) {
        filter.eventDate.$lte = new Date(toDate as string);
      }
    }

    if (search && search !== '') {
      const searchRegex = new RegExp(search as string, 'i');
      filter.$or = [{ name: searchRegex }, { note: searchRegex }];
    }

    let sort: any = { eventDate: -1 };

    if (sortBy) {
      const sortField = sortBy === 'date' ? 'eventDate' : (sortBy as string);
      sort = { [sortField]: sortDirection === 'asc' ? 1 : -1 };
    }

    const expenses = await Expense.find(filter).sort(sort);

    res.status(200).json({
      success: true,
      count: expenses.length,
      data: expenses,
    });
  } catch (error) {
    next(error);
  }
};

export const getExpense = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const expense = await Expense.findById(req.params.id);

    if (!expense) {
      const error: AppError = new Error('Expense not found');
      error.statusCode = 404;
      return next(error);
    }

    if (expense.userId !== req.user.id && req.user.role !== 'admin') {
      const error: AppError = new Error('Not authorized to access this expense');
      error.statusCode = 403;
      return next(error);
    }

    res.status(200).json({
      success: true,
      data: expense,
    });
  } catch (error) {
    next(error);
  }
};

export const createExpense = async (req: Request, res: Response, next: NextFunction) => {
  try {
    req.body.userId = req.user.id;

    const expense = await Expense.create(req.body);

    res.status(201).json({
      success: true,
      data: expense,
    });
  } catch (error) {
    next(error);
  }
};

export const updateExpense = async (req: Request, res: Response, next: NextFunction) => {
  try {
    let expense = await Expense.findById(req.params.id);

    if (!expense) {
      const error: AppError = new Error('Expense not found');
      error.statusCode = 404;
      return next(error);
    }

    if (expense.userId !== req.user.id && req.user.role !== 'admin') {
      const error: AppError = new Error('Not authorized to update this expense');
      error.statusCode = 403;
      return next(error);
    }

    expense = await Expense.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      data: expense,
    });
  } catch (error) {
    next(error);
  }
};

export const deleteExpense = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const expense = await Expense.findById(req.params.id);

    if (!expense) {
      const error: AppError = new Error('Expense not found');
      error.statusCode = 404;
      return next(error);
    }

    // Check if expense belongs to user
    if (expense.userId !== req.user.id && req.user.role !== 'admin') {
      const error: AppError = new Error('Not authorized to delete this expense');
      error.statusCode = 403;
      return next(error);
    }

    await expense.deleteOne();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    next(error);
  }
};

// Type definitions for expense stats
interface ExpenseCategoryItem {
  name: string;
  value: number;
}

interface MonthlyDataItem {
  name: string;
  income: number;
  expenses: number;
}

interface ExpenseStatsResponse {
  groupedExpense: Record<string, number>;
  expensesByCategory: ExpenseCategoryItem[];
  monthlyData: MonthlyDataItem[];
  recentTransactions: IExpense[];
}

// Helper function to create date filter
const createDateFilter = (
  fromDate?: string,
  toDate?: string,
  ignoreDate?: string,
): Record<string, Date> => {
  if (ignoreDate === 'true') {
    return {};
  }

  const dateFilter: Record<string, Date> = {};
  const today = new Date();

  if (fromDate) {
    dateFilter.$gte = new Date(fromDate);
  } else {
    // Default to first day of current month
    dateFilter.$gte = new Date(today.getFullYear(), today.getMonth(), 1);
  }

  if (toDate) {
    dateFilter.$lte = new Date(toDate);
  } else if (!fromDate) {
    // Default to last day of current month
    dateFilter.$lte = new Date(today.getFullYear(), today.getMonth() + 1, 0);
  }

  return dateFilter;
};

export const getExpenseStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { fromDate, toDate, ignoreDate } = req.query;
    const userId = req.user.id;

    // Create date filter
    const dateFilter = createDateFilter(
      fromDate as string | undefined,
      toDate as string | undefined,
      ignoreDate as string | undefined,
    );

    // Create match conditions
    const hasDateFilter = Object.keys(dateFilter).length > 0;
    const baseMatch = { userId };
    const matchCondition = {
      ...baseMatch,
      ...(hasDateFilter ? { eventDate: dateFilter } : {}),
    };

    // Use a single aggregation pipeline with facets to get all data in one query
    const results = await Expense.aggregate([
      { $match: matchCondition },
      {
        $facet: {
          // Get expenses by category
          expensesByCategory: [
            { $match: { type: 'EXPENSE' } },
            { $group: { _id: '$category', total: { $sum: '$amount' } } },
            { $sort: { total: -1 } },
            { $project: { _id: 0, name: '$_id', value: { $abs: '$total' } } },
          ],

          // Get grouped expenses by type
          groupedExpense: [{ $group: { _id: '$type', total: { $sum: '$amount' } } }],

          // Get monthly data
          monthlyData: [
            {
              $group: {
                _id: {
                  year: { $year: '$eventDate' },
                  month: { $month: '$eventDate' },
                  type: '$type',
                },
                total: { $sum: '$amount' },
              },
            },
            { $sort: { '_id.year': 1, '_id.month': 1 } },
          ],

          // Get recent transactions
          recentTransactions: [{ $sort: { eventDate: -1 } }, { $limit: 5 }],
        },
      },
    ]);

    // Extract results from facets
    const [aggregationResult] = results;
    const { expensesByCategory, groupedExpense, monthlyData, recentTransactions } =
      aggregationResult;

    // Format monthly data
    const monthlyDataMap = new Map<string, MonthlyDataItem>();
    const MONTH_NAMES = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    monthlyData.forEach((item: any) => {
      const monthIndex = item._id.month - 1;
      const monthName = MONTH_NAMES[monthIndex];

      if (!monthlyDataMap.has(monthName)) {
        monthlyDataMap.set(monthName, {
          name: monthName,
          income: 0,
          expenses: 0,
        });
      }

      // We know this exists because we just created it if it didn't exist
      const monthData = monthlyDataMap.get(monthName);

      if (monthData && item._id.type === 'INCOME') {
        monthData.income = Math.abs(item.total);
      } else if (monthData && item._id.type === 'EXPENSE') {
        monthData.expenses = Math.abs(item.total);
      }
    });

    // Convert grouped expense to object
    const groupedExpenseObj = groupedExpense.reduce((acc: Record<string, number>, item: any) => {
      acc[item._id] = item.total;
      return acc;
    }, {});

    // Prepare response
    const response: ExpenseStatsResponse = {
      groupedExpense: groupedExpenseObj,
      expensesByCategory,
      monthlyData: Array.from(monthlyDataMap.values()),
      recentTransactions,
    };

    res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    next(error);
  }
};
