import mongoose, { Document, Schema } from 'mongoose';

export type ExpenseType =
  | 'INCOME'
  | 'EXPENSE'
  | 'DEBT_BOUGHT'
  | 'DEBT_GIVEN'
  | 'INVESTMENT'
  | 'INCOME_TAX';

export interface IExpense extends Document {
  type: ExpenseType;
  name?: string;
  note?: string;
  amount: number;
  userId: string;
  category: string;
  eventDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ExpenseSchema = new Schema<IExpense>({
  type: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    trim: true,
  },
  note: {
    type: String,
    trim: true,
  },
  // negative reprsents an expense /debt_bought etc, positive represents an income
  amount: {
    type: Number,
    required: [true, 'Please add a positive or negative number'],
  },
  userId: {
    type: String,
  },
  category: {
    type: String,
    required: [true, 'Please add a category'],
  },
  eventDate: {
    type: Date,
    default: Date.now,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

export const Expense = mongoose.model<IExpense>('expense', ExpenseSchema);
