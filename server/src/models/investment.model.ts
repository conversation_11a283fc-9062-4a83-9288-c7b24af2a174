import mongoose, { Document, Schema } from 'mongoose';

// Schema for individual purchase entries
const PurchaseSchema = new Schema(
  {
    price: {
      type: Number,
      required: true,
    },
    quantity: {
      type: Number,
      required: true,
    },
  },
  { _id: false }, // prevents _id from being added to each subdocument
);

const InvestmentSchema = new Schema<any>({
  name: {
    type: String,
    required: [true, 'Please add a name'],
  },
  symbol: {
    type: String,
    required: [true, 'Please add a symbol'],
  },
  // Legacy field kept for backward compatibility
  purchasedPrice: {
    type: Number,
  },
  type: {
    type: String,
    enum: ['STOCK', 'MUTUAL_FUND', 'GOLD', 'SILVER'],
  },
  purpose: {
    type: String,
    enum: ['MONITORING', 'OWNED'],
  },
  // Legacy field kept for backward compatibility
  quantity: {
    type: Number,
  },
  // New field for multiple purchases
  purchases: {
    type: [PurchaseSchema],
    default: [],
  },
  targetPrice: {
    type: Number,
  },
  userId: {
    type: String,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

export const Investments = mongoose.model<any>('investments', InvestmentSchema);
