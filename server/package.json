{"name": "blazestack-server", "version": "1.0.0", "description": "Express MongoDB TypeScript server with authentication", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon", "build": "tsc", "build:w": "tsc -w", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "test": "jest"}, "keywords": ["express", "mongodb", "typescript", "authentication"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.1.0", "morgan": "^1.10.0", "winston": "^3.8.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.1", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/node": "^18.16.3", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "nodemon": "^2.0.22", "prettier": "^2.8.8", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.4"}}