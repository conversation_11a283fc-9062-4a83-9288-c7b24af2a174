{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Server",
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceFolder}/server",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["run", "dev"],
      "env": {
        "NODE_ENV": "development",
        "PORT": "5000",
        "MONGODB_URI": "mongodb://localhost:27017/blazestack",
        "JWT_SECRET": "dev_jwt_secret",
        "COOKIE_SECRET": "dev_cookie_secret"
      },
      "console": "integratedTerminal",
      "outFiles": ["${workspaceFolder}/server/dist/**/*.js"]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Client",
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceFolder}/client",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["run", "dev"],
      "console": "integratedTerminal"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Server (Build)",
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceFolder}/server",
      "program": "${workspaceFolder}/server/dist/index.js",
      "preLaunchTask": "npm: build - server",
      "env": {
        "NODE_ENV": "development",
        "PORT": "5000",
        "MONGODB_URI": "mongodb://localhost:27017/blazestack",
        "JWT_SECRET": "dev_jwt_secret",
        "COOKIE_SECRET": "dev_cookie_secret"
      },
      "outFiles": ["${workspaceFolder}/server/dist/**/*.js"]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Jest Tests",
      "cwd": "${workspaceFolder}/server",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["test"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    }
  ]
}
