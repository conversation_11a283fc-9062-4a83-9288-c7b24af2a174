{"eslint.alwaysShowStatus": true, "eslint.format.enable": true, "eslint.run": "onType", "eslint.workingDirectories": ["./server", "./client"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit", "source.fixAll": "explicit"}, "editor.insertSpaces": true, "editor.detectIndentation": true, "editor.tabSize": 2, "editor.rulers": [100], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "files.eol": "\n"}