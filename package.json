{"name": "blazestack", "version": "1.0.0", "description": "Full-stack application with Express, MongoDB, React, and TypeScript", "main": "/server/dist/index.js", "scripts": {"start": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "build": "cd client && npm run build && cd ../server && npm run build"}, "keywords": ["express", "mongodb", "react", "typescript", "authentication"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^22.15.2", "concurrently": "^8.2.2"}}